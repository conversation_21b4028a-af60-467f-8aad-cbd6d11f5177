#!/usr/bin/env python3
"""
测试添加患者API的实际HTTP请求
模拟前端发送的真实请求
"""

import requests
import json
import sys
import os

# 后端服务地址
BASE_URL = 'http://localhost:5000'

def login_user(username, password):
    """用户登录获取token"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        'username': username,
        'password': password
    }

    try:
        response = requests.post(login_url, json=login_data)
        print(f"🔍 登录请求状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"🔍 登录响应: {result}")

            if result.get('success'):
                token = result.get('data', {}).get('token')  # 修正：token在data字段中
                user_info = result.get('data', {}).get('user', {})  # 修正：user在data字段中
                print(f"✅ {user_info.get('role')} {username} 登录成功")
                return token, user_info
            else:
                print(f"❌ 登录失败: {result.get('error')}")
                return None, None
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None, None
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端服务正在运行")
        return None, None
    except Exception as e:
        print(f"❌ 登录过程中出错: {e}")
        return None, None

def add_patient(token, patient_data):
    """添加患者"""
    add_url = f"{BASE_URL}/api/personnel"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(add_url, json=patient_data, headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            patient = result.get('data', {})
            print(f"✅ 患者添加成功:")
            print(f"   姓名: {patient.get('name')}")
            print(f"   手机号: {patient.get('phone')}")
            print(f"   分配医生: {patient.get('assigned_doctor', '未分配')}")
            print(f"   医生姓名: {patient.get('assigned_doctor_name', '未分配')}")
            print(f"   分配日期: {patient.get('assignment_date', '未分配')}")
            return patient
        else:
            print(f"❌ 添加患者失败: {result.get('error')}")
            return None
    except Exception as e:
        print(f"❌ 添加患者请求出错: {e}")
        return None

def get_patients(token):
    """获取患者列表"""
    get_url = f"{BASE_URL}/api/personnel"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(get_url, headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            patients = result.get('data', [])
            total = result.get('total', 0)
            print(f"✅ 获取患者列表成功: 共 {total} 个患者")
            return patients
        else:
            print(f"❌ 获取患者列表失败: {result.get('error')}")
            return []
    except Exception as e:
        print(f"❌ 获取患者列表请求出错: {e}")
        return []

def delete_patient(token, patient_id):
    """删除患者"""
    delete_url = f"{BASE_URL}/api/personnel/{patient_id}"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.delete(delete_url, headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            print(f"✅ 患者删除成功")
            return True
        else:
            print(f"❌ 删除患者失败: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ 删除患者请求出错: {e}")
        return False

def test_admin_workflow():
    """测试管理员工作流程"""
    print("🔍 测试管理员工作流程...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败，跳过测试")
        return False
    
    # 添加患者
    patient_data = {
        'name': 'API测试患者A',
        'phone': '13800000001',
        'age': 45,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    patient = add_patient(token, patient_data)
    if not patient:
        return False
    
    # 验证患者没有自动分配医生
    if patient.get('assigned_doctor'):
        print("❌ 管理员添加的患者不应该自动分配医生")
        return False
    
    print("✅ 管理员添加患者正确：未自动分配医生")
    
    # 清理：删除测试患者
    patient_id = patient.get('id') or patient.get('_id')
    if patient_id:
        delete_patient(token, patient_id)
    
    return True

def test_doctor_workflow():
    """测试医生工作流程"""
    print("\n🔍 测试医生工作流程...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败，跳过测试")
        return False
    
    # 获取医生当前的患者数量
    patients_before = get_patients(token)
    count_before = len(patients_before)
    
    # 添加患者
    patient_data = {
        'name': 'API测试患者B',
        'phone': '13800000002',
        'age': 35,
        'gender': '女',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    patient = add_patient(token, patient_data)
    if not patient:
        return False
    
    # 验证患者自动分配给当前医生
    if patient.get('assigned_doctor') != 'doctor01':
        print(f"❌ 医生添加的患者应该自动分配给当前医生，实际分配给: {patient.get('assigned_doctor')}")
        return False
    
    print("✅ 医生添加患者正确：自动分配给当前医生")
    
    # 验证医生只能看到自己的患者
    patients_after = get_patients(token)
    count_after = len(patients_after)
    
    if count_after == count_before + 1:
        print("✅ 医生患者列表正确更新")
    else:
        print(f"❌ 医生患者列表更新异常: 之前{count_before}个，现在{count_after}个")
    
    # 验证新添加的患者在列表中
    new_patient_found = False
    for p in patients_after:
        if p.get('phone') == patient_data['phone']:
            new_patient_found = True
            break
    
    if new_patient_found:
        print("✅ 新添加的患者出现在医生的患者列表中")
    else:
        print("❌ 新添加的患者未出现在医生的患者列表中")
    
    # 清理：删除测试患者
    patient_id = patient.get('id') or patient.get('_id')
    if patient_id:
        delete_patient(token, patient_id)
    
    return True

def test_cross_doctor_isolation():
    """测试医生间的数据隔离"""
    print("\n🔍 测试医生间的数据隔离...")
    
    # 第一个医生登录并添加患者
    token1, user1 = login_user('doctor01', 'doctor123')
    if not token1:
        print("❌ 第一个医生登录失败")
        return False
    
    patient_data1 = {
        'name': 'API测试患者C',
        'phone': '13800000003',
        'age': 40,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    patient1 = add_patient(token1, patient_data1)
    if not patient1:
        return False
    
    # 第二个医生登录
    token2, user2 = login_user('HUyq', '123456')
    if not token2:
        print("❌ 第二个医生登录失败")
        # 清理第一个患者
        delete_patient(token1, patient1.get('id'))
        return False
    
    # 第二个医生获取患者列表，应该看不到第一个医生的患者
    patients2 = get_patients(token2)
    
    # 检查是否能看到第一个医生添加的患者
    can_see_other_patient = False
    for p in patients2:
        if p.get('phone') == patient_data1['phone']:
            can_see_other_patient = True
            break
    
    if can_see_other_patient:
        print("❌ 医生能看到其他医生的患者，权限隔离失败")
        result = False
    else:
        print("✅ 医生无法看到其他医生的患者，权限隔离正常")
        result = True
    
    # 清理测试数据
    patient_id = patient1.get('id') or patient1.get('_id')
    if patient_id:
        delete_patient(token1, patient_id)
    
    return result

def main():
    """主函数"""
    print("🧪 开始测试添加患者API的实际HTTP请求...\n")
    
    # 检查后端服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未正常运行，请先启动后端服务")
            return
    except:
        print("❌ 无法连接到后端服务，请确保后端服务在 http://localhost:5000 运行")
        print("💡 启动命令: cd backend && python app.py")
        return
    
    print("✅ 后端服务连接正常\n")
    
    try:
        # 测试管理员工作流程
        admin_test_passed = test_admin_workflow()
        
        # 测试医生工作流程
        doctor_test_passed = test_doctor_workflow()
        
        # 测试医生间数据隔离
        isolation_test_passed = test_cross_doctor_isolation()
        
        print("\n" + "="*60)
        print("📊 API测试结果总结:")
        print("="*60)
        print(f"管理员工作流程测试: {'✅ 通过' if admin_test_passed else '❌ 失败'}")
        print(f"医生工作流程测试: {'✅ 通过' if doctor_test_passed else '❌ 失败'}")
        print(f"医生数据隔离测试: {'✅ 通过' if isolation_test_passed else '❌ 失败'}")
        
        if admin_test_passed and doctor_test_passed and isolation_test_passed:
            print("\n✅ 所有API测试通过！")
            print("🎯 前端可以正常使用以下功能:")
            print("   - 管理员添加患者（不自动分配医生）")
            print("   - 医生添加患者（自动分配给当前医生）")
            print("   - 医生只能看到自己管理的患者")
            print("   - 权限隔离正常工作")
        else:
            print("\n❌ 部分API测试失败，请检查后端实现")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
