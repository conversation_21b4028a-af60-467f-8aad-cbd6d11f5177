#!/usr/bin/env python3
"""
测试医生-患者关系查询功能
演示如何根据医生用户名查询其管理的患者
"""

import pymongo
from datetime import datetime
import sys

def connect_to_database():
    """连接到MongoDB数据库"""
    try:
        mongo_url = 'mongodb://localhost:27017'
        client = pymongo.MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
        client.server_info()
        db = client['med_call_records']
        print(f"✅ 成功连接到数据库: {mongo_url}")
        return client, db
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None, None

def get_patients_by_doctor(db, doctor_username):
    """根据医生用户名查询其管理的患者"""
    try:
        patients = list(db.personnel.find({
            "assigned_doctor": doctor_username
        }))
        
        print(f"\n👨‍⚕️ 医生 {doctor_username} 管理的患者:")
        if not patients:
            print("   没有分配的患者")
            return []
        
        for patient in patients:
            print(f"   - {patient['name']} ({patient['phone']})")
            print(f"     年龄: {patient['age']}, 性别: {patient['gender']}")
            print(f"     训练状态: {patient['training_status']}")
            print(f"     分配日期: {patient.get('assignment_date', 'N/A')}")
            print()
        
        return patients
    
    except Exception as e:
        print(f"❌ 查询患者失败: {e}")
        return []

def get_call_records_by_doctor(db, doctor_username):
    """根据医生用户名查询其患者的通话记录"""
    try:
        # 首先获取该医生管理的患者
        patients = list(db.personnel.find({
            "assigned_doctor": doctor_username
        }, {"phone": 1, "name": 1}))
        
        if not patients:
            print(f"医生 {doctor_username} 没有分配的患者")
            return []
        
        # 获取患者的手机号列表
        patient_phones = [patient['phone'] for patient in patients]
        
        # 查询这些患者的通话记录
        call_records = list(db.call_records.find({
            "手机号": {"$in": patient_phones}
        }).sort("创建时间", -1).limit(10))  # 最近10条记录
        
        print(f"\n📞 医生 {doctor_username} 患者的最近通话记录:")
        if not call_records:
            print("   没有通话记录")
            return []
        
        for record in call_records:
            print(f"   - 患者: {record.get('患者名字')} ({record.get('手机号')})")
            print(f"     通话时间: {record.get('通话时间')}")
            print(f"     拨号状态: {record.get('拨号状态')}")
            if record.get('训练完成情况'):
                print(f"     训练情况: {record.get('训练完成情况')}")
            print()
        
        return call_records
    
    except Exception as e:
        print(f"❌ 查询通话记录失败: {e}")
        return []

def get_doctor_statistics(db, doctor_username):
    """获取医生的统计信息"""
    try:
        # 患者总数
        total_patients = db.personnel.count_documents({
            "assigned_doctor": doctor_username
        })
        
        # 训练中的患者数
        training_patients = db.personnel.count_documents({
            "assigned_doctor": doctor_username,
            "training_status": "训练中"
        })
        
        # 获取患者手机号
        patients = list(db.personnel.find({
            "assigned_doctor": doctor_username
        }, {"phone": 1}))
        patient_phones = [p['phone'] for p in patients]
        
        # 通话记录总数
        total_calls = db.call_records.count_documents({
            "手机号": {"$in": patient_phones}
        }) if patient_phones else 0
        
        # 最近7天的通话记录
        from datetime import datetime, timedelta
        seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        recent_calls = db.call_records.count_documents({
            "手机号": {"$in": patient_phones},
            "记录日期": {"$gte": seven_days_ago}
        }) if patient_phones else 0
        
        print(f"\n📊 医生 {doctor_username} 的统计信息:")
        print(f"   管理患者总数: {total_patients}")
        print(f"   训练中患者数: {training_patients}")
        print(f"   通话记录总数: {total_calls}")
        print(f"   最近7天通话: {recent_calls}")
        
        return {
            'total_patients': total_patients,
            'training_patients': training_patients,
            'total_calls': total_calls,
            'recent_calls': recent_calls
        }
    
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        return {}

def test_all_doctors(db):
    """测试所有医生的查询功能"""
    print("🔍 测试所有医生的查询功能...\n")
    
    # 获取所有医生
    doctors = list(db.sys_info.find({
        "doc_type": "user_account",
        "role": "医生",
        "is_active": True
    }))
    
    for doctor in doctors:
        username = doctor['username']
        full_name = doctor['full_name']
        
        print(f"{'='*60}")
        print(f"测试医生: {full_name} ({username})")
        print(f"{'='*60}")
        
        # 查询患者
        patients = get_patients_by_doctor(db, username)
        
        # 查询通话记录
        call_records = get_call_records_by_doctor(db, username)
        
        # 获取统计信息
        stats = get_doctor_statistics(db, username)
        
        print()

def main():
    """主函数"""
    print("🧪 开始测试医生-患者关系查询功能...")
    
    # 连接数据库
    client, db = connect_to_database()
    if client is None or db is None:
        print("❌ 无法连接到数据库，退出程序")
        sys.exit(1)
    
    try:
        # 测试所有医生
        test_all_doctors(db)
        
        print("\n" + "="*60)
        print("✅ 医生-患者关系查询功能测试完成！")
        print("="*60)
        
        print("\n💡 前端开发建议:")
        print("1. 医生登录后，使用 assigned_doctor 字段过滤患者列表")
        print("2. 查询通话记录时，先获取医生的患者手机号列表，再查询通话记录")
        print("3. 统计信息可以通过聚合查询获得更好的性能")
        print("4. 建议在 assigned_doctor 字段上创建索引以提高查询性能")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        if client is not None:
            client.close()
            print("\n🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
