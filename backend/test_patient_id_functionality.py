#!/usr/bin/env python3
"""
测试患者编号功能
验证添加和更新患者时的编号验证
"""

import requests
import json
import sys
import os

# 后端服务地址
BASE_URL = 'http://localhost:5000'

def login_user(username, password):
    """用户登录获取token"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token = result.get('data', {}).get('token')
                user_info = result.get('data', {}).get('user', {})
                print(f"✅ {user_info.get('role')} {username} 登录成功")
                return token, user_info
            else:
                print(f"❌ 登录失败: {result.get('error')}")
                return None, None
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ 登录过程中出错: {e}")
        return None, None

def add_patient(token, patient_data):
    """添加患者"""
    add_url = f"{BASE_URL}/api/personnel"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(add_url, json=patient_data, headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            patient = result.get('data', {})
            print(f"✅ 患者添加成功:")
            print(f"   编号: {patient.get('patient_id')}")
            print(f"   姓名: {patient.get('name')}")
            print(f"   手机号: {patient.get('phone')}")
            return patient
        else:
            print(f"❌ 添加患者失败: {result.get('error')}")
            return None
    except Exception as e:
        print(f"❌ 添加患者请求出错: {e}")
        return None

def update_patient(token, patient_id, update_data):
    """更新患者信息"""
    update_url = f"{BASE_URL}/api/personnel/{patient_id}"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.put(update_url, json=update_data, headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            patient = result.get('data', {})
            print(f"✅ 患者更新成功:")
            print(f"   编号: {patient.get('patient_id')}")
            print(f"   姓名: {patient.get('name')}")
            return patient
        else:
            print(f"❌ 更新患者失败: {result.get('error')}")
            return None
    except Exception as e:
        print(f"❌ 更新患者请求出错: {e}")
        return None

def delete_patient(token, patient_id):
    """删除患者"""
    delete_url = f"{BASE_URL}/api/personnel/{patient_id}"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.delete(delete_url, headers=headers)
        result = response.json()
        
        if response.status_code == 200 and result.get('success'):
            print(f"✅ 患者删除成功")
            return True
        else:
            print(f"❌ 删除患者失败: {result.get('error')}")
            return False
    except Exception as e:
        print(f"❌ 删除患者请求出错: {e}")
        return False

def test_add_patient_with_id():
    """测试添加带编号的患者"""
    print("🔍 测试添加带编号的患者...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败，跳过测试")
        return False
    
    # 添加患者
    patient_data = {
        'patient_id': 'TEST001',
        'name': '测试患者编号A',
        'phone': '13900000101',
        'age': 30,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    patient = add_patient(token, patient_data)
    if not patient:
        return False
    
    # 验证患者编号
    if patient.get('patient_id') != 'TEST001':
        print(f"❌ 患者编号不正确: 期望 TEST001，实际 {patient.get('patient_id')}")
        return False
    
    # 验证自动分配医生
    if patient.get('assigned_doctor') != 'doctor01':
        print(f"❌ 医生分配不正确: 期望 doctor01，实际 {patient.get('assigned_doctor')}")
        return False
    
    print("✅ 添加带编号的患者测试通过")
    
    # 清理：删除测试患者
    patient_id = patient.get('id') or patient.get('_id')
    if patient_id:
        delete_patient(token, patient_id)
    
    return True

def test_duplicate_patient_id():
    """测试重复患者编号验证"""
    print("\n🔍 测试重复患者编号验证...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败，跳过测试")
        return False
    
    # 添加第一个患者
    patient_data1 = {
        'patient_id': 'TEST002',
        'name': '测试患者编号B1',
        'phone': '13900000102',
        'age': 25,
        'gender': '女',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    patient1 = add_patient(token, patient_data1)
    if not patient1:
        return False
    
    # 尝试添加相同编号的第二个患者
    patient_data2 = {
        'patient_id': 'TEST002',  # 相同编号
        'name': '测试患者编号B2',
        'phone': '13900000103',
        'age': 28,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    print("   尝试添加重复编号的患者...")
    patient2 = add_patient(token, patient_data2)
    
    if patient2:
        print("❌ 重复编号验证失败：应该拒绝重复编号")
        # 清理两个患者
        delete_patient(token, patient1.get('id') or patient1.get('_id'))
        delete_patient(token, patient2.get('id') or patient2.get('_id'))
        return False
    else:
        print("✅ 重复编号验证成功：正确拒绝了重复编号")
    
    # 清理第一个患者
    patient_id = patient1.get('id') or patient1.get('_id')
    if patient_id:
        delete_patient(token, patient_id)
    
    return True

def test_update_patient_id():
    """测试更新患者编号"""
    print("\n🔍 测试更新患者编号...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败，跳过测试")
        return False
    
    # 添加患者
    patient_data = {
        'patient_id': 'TEST003',
        'name': '测试患者编号C',
        'phone': '13900000104',
        'age': 35,
        'gender': '女',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    patient = add_patient(token, patient_data)
    if not patient:
        return False
    
    patient_id = patient.get('id') or patient.get('_id')
    
    # 更新患者编号
    update_data = {
        'patient_id': 'TEST003_UPDATED'
    }
    
    updated_patient = update_patient(token, patient_id, update_data)
    if not updated_patient:
        delete_patient(token, patient_id)
        return False
    
    # 验证编号更新
    if updated_patient.get('patient_id') != 'TEST003_UPDATED':
        print(f"❌ 编号更新失败: 期望 TEST003_UPDATED，实际 {updated_patient.get('patient_id')}")
        delete_patient(token, patient_id)
        return False
    
    print("✅ 患者编号更新测试通过")
    
    # 清理患者
    delete_patient(token, patient_id)
    
    return True

def test_empty_patient_id():
    """测试空编号验证"""
    print("\n🔍 测试空编号验证...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败，跳过测试")
        return False
    
    # 尝试添加空编号的患者
    patient_data = {
        'patient_id': '',  # 空编号
        'name': '测试患者编号D',
        'phone': '13900000105',
        'age': 40,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    print("   尝试添加空编号的患者...")
    patient = add_patient(token, patient_data)
    
    if patient:
        print("❌ 空编号验证失败：应该拒绝空编号")
        delete_patient(token, patient.get('id') or patient.get('_id'))
        return False
    else:
        print("✅ 空编号验证成功：正确拒绝了空编号")
    
    return True

def main():
    """主函数"""
    print("🧪 开始测试患者编号功能...\n")
    
    # 检查后端服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未正常运行，请先启动后端服务")
            return
    except:
        print("❌ 无法连接到后端服务，请确保后端服务在 http://localhost:5000 运行")
        return
    
    print("✅ 后端服务连接正常\n")
    
    try:
        # 测试添加带编号的患者
        test1_passed = test_add_patient_with_id()
        
        # 测试重复编号验证
        test2_passed = test_duplicate_patient_id()
        
        # 测试更新患者编号
        test3_passed = test_update_patient_id()
        
        # 测试空编号验证
        test4_passed = test_empty_patient_id()
        
        print("\n" + "="*60)
        print("📊 患者编号功能测试结果总结:")
        print("="*60)
        print(f"添加带编号患者测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
        print(f"重复编号验证测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
        print(f"更新患者编号测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
        print(f"空编号验证测试: {'✅ 通过' if test4_passed else '❌ 失败'}")
        
        if all([test1_passed, test2_passed, test3_passed, test4_passed]):
            print("\n✅ 所有患者编号功能测试通过！")
            print("🎯 功能实现正确:")
            print("   - 添加患者时必须提供编号")
            print("   - 患者编号具有唯一性约束")
            print("   - 可以更新患者编号")
            print("   - 正确验证空编号")
            print("   - 医生添加患者时自动分配给当前医生")
        else:
            print("\n❌ 部分患者编号功能测试失败，请检查后端实现")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
