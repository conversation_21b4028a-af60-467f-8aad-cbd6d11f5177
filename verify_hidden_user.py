#!/usr/bin/env python3
"""
验证隐藏用户的数据库状态
"""

import pymongo
from bson import ObjectId

def main():
    """验证隐藏用户"""
    try:
        # 连接数据库
        client = pymongo.MongoClient('mongodb://localhost:27017')
        db = client['med_call_records']
        
        print("🔍 查询所有医生账号（包括隐藏的）...")
        
        # 查询所有医生账号
        all_doctors = list(db.sys_info.find({
            'doc_type': 'user_account',
            'role': '医生'
        }))
        
        print(f"\n📊 总共找到 {len(all_doctors)} 个医生账号：")
        
        for doctor in all_doctors:
            username = doctor.get('username', 'N/A')
            full_name = doctor.get('full_name', 'N/A')
            is_active = doctor.get('is_active', False)
            is_hidden = doctor.get('is_hidden', False)
            
            status_parts = []
            if is_active:
                status_parts.append("激活")
            else:
                status_parts.append("停用")
                
            if is_hidden:
                status_parts.append("隐藏")
            
            status = " | ".join(status_parts)
            
            print(f"  - {full_name} ({username}) - {status}")
        
        # 查询显示的医生账号（API返回的）
        visible_doctors = list(db.sys_info.find({
            'doc_type': 'user_account',
            'role': '医生',
            'is_hidden': {'$ne': True}
        }))
        
        print(f"\n👁️  API显示的医生账号数量: {len(visible_doctors)}")
        print(f"🔒 隐藏的医生账号数量: {len(all_doctors) - len(visible_doctors)}")
        
        # 查找隐藏的账号
        hidden_doctors = list(db.sys_info.find({
            'doc_type': 'user_account',
            'role': '医生',
            'is_hidden': True
        }))
        
        if hidden_doctors:
            print(f"\n🙈 隐藏的账号详情:")
            for doctor in hidden_doctors:
                username = doctor.get('username', 'N/A')
                full_name = doctor.get('full_name', 'N/A')
                is_active = doctor.get('is_active', False)
                print(f"  - {full_name} ({username}) - {'激活' if is_active else '停用'}")
        
        client.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    main()
